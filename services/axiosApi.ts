import axios from 'axios';
import env from '../env';

const instance = axios.create({
  baseURL: env.BASE_URL,
  timeout: 8000,
});

// 请求拦截器
instance.interceptors.request.use(
  config => {
    // 比如加 token
    const token = 'your_token';
    if (token) config.headers.Authorization = `Bearer ${token}`;
    return config;
  },
  error => Promise.reject(error)
);

// 响应拦截器
instance.interceptors.response.use(
  response => response.data, // 直接返回 data
  error => {
    if (error.response?.status === 401) {
      // 处理未登录逻辑
    }
    return Promise.reject(error);
  }
);

export default instance;
