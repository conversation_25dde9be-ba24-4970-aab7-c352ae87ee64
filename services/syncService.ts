import { SQLiteDatabase } from 'expo-sqlite';

import { Storage } from 'expo-sqlite/kv-store';

import env from '../env';



// 同步结果类型
export interface SyncResult {
    success: boolean;
    message: string;
    mongoId?: string;
    syncTime?: string;
}

// 笔记数据接口
export interface NoteData {
    id?: number;
    title: string;
    content: string;
    mood_name: string;
    mood_score: number;
    mood_app_in_img_name: string;
    mood_user_create_url?: string | null;
    yyyymm: number;
    created_at: string;
    updated_at: string;
}

// 同步服务类
export class SyncService {
    private static instance: SyncService;
    
    public static getInstance(): SyncService {
        if (!SyncService.instance) {
            SyncService.instance = new SyncService();
        }
        return SyncService.instance;
    }

    private constructor() {}

    /**
     * 获取用户token
     */
    private async getUserToken(): Promise<string> {
        const token = await Storage.getItem('userToken');
        if (!token) {
            throw new Error('请先登录后再进行同步');
        }
        return token;
    }

    /**
     * 同步单个笔记到云端
     */
    public async syncNoteToCloud(noteData: NoteData): Promise<SyncResult> {
        try {
            const token = await this.getUserToken();

            const response = await fetch(env.BASE_URL + '/x_mooood_note', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`,
                },
                body: JSON.stringify({
                    do_sth: 'add_one_note',
                    title: noteData.title,
                    content: noteData.content,
                    mood_name: noteData.mood_name,
                    mood_score: noteData.mood_score,
                    mood_app_in_img_name: noteData.mood_app_in_img_name,
                    mood_user_create_url: noteData.mood_user_create_url,
                    yyyymm: noteData.yyyymm,
                    created_at: noteData.created_at,
                    updated_at: noteData.updated_at,
                }),
            });

            if (response.ok) {
                const result = await response.json();
                return {
                    success: true,
                    message: '同步成功',
                    mongoId: result.data?._id,
                    syncTime: result.data?.current_time,
                };
            } else {
                const errorData = await response.json().catch(() => ({}));
                console.error('云端同步失败:', response.status, errorData);
                return {
                    success: false,
                    message: errorData?.msg || '云端同步失败',
                };
            }
        } catch (error) {
            console.error('同步笔记到云端时出错:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : '同步过程中发生未知错误',
            };
        }
    }

    /**
     * 更新本地数据库的同步字段
     */
    public async updateLocalSyncInfo(
        db: SQLiteDatabase, 
        localNoteId: number, 
        mongoId: string, 
        syncTime: string
    ): Promise<boolean> {
        try {
            await db.runAsync(
                `UPDATE notes SET 
                    mongodb_id = ?, 
                    sync_mongodb_time = ? 
                WHERE id = ?`,
                [mongoId, syncTime, localNoteId]
            );
            console.log('本地数据库同步字段更新成功，MongoDB ID:', mongoId);
            return true;
        } catch (error) {
            console.error('更新本地数据库同步字段失败:', error);
            return false;
        }
    }

    /**
     * 同步笔记并更新本地数据库
     */
    public async syncNoteWithLocalUpdate(
        db: SQLiteDatabase,
        noteData: NoteData & { id: number }
    ): Promise<SyncResult> {
        const syncResult = await this.syncNoteToCloud(noteData);
        
        if (syncResult.success && syncResult.mongoId && syncResult.syncTime) {
            const updateSuccess = await this.updateLocalSyncInfo(
                db, 
                noteData.id, 
                syncResult.mongoId, 
                syncResult.syncTime
            );
            
            if (!updateSuccess) {
                return {
                    success: false,
                    message: '云端同步成功，但本地数据库更新失败',
                };
            }
        }
        
        return syncResult;
    }

    /**
     * 获取未同步的笔记
     */
    public async getUnsyncedNotes(
        db: SQLiteDatabase, 
        dateFilter?: string
    ): Promise<any[]> {
        try {
            let query = `
                SELECT * FROM notes 
                WHERE mongodb_id IS NULL
                ORDER BY created_at DESC
            `;
            let params: any[] = [];

            if (dateFilter) {
                query = `
                    SELECT * FROM notes 
                    WHERE date(created_at) = date(?) 
                    AND mongodb_id IS NULL
                    ORDER BY created_at DESC
                `;
                params = [dateFilter];
            }

            const result = await db.getAllAsync(query, params);
            return result as any[];
        } catch (error) {
            console.error('获取未同步笔记时出错:', error);
            return [];
        }
    }

    /**
     * 批量同步笔记
     */
    public async batchSyncNotes(
        db: SQLiteDatabase,
        notes: any[],
        onProgress?: (current: number, total: number) => void
    ): Promise<{ successCount: number; failCount: number }> {
        let successCount = 0;
        let failCount = 0;
        const total = notes.length;

        for (let i = 0; i < notes.length; i++) {
            const note = notes[i];
            onProgress?.(i + 1, total);

            const result = await this.syncNoteWithLocalUpdate(db, note);
            if (result.success) {
                successCount++;
            } else {
                failCount++;
            }
        }

        return { successCount, failCount };
    }
}

// 导出单例实例
export const syncService = SyncService.getInstance();

// 导出一些常用的快捷方法
export const syncNote = (db: SQLiteDatabase, noteData: NoteData & { id: number }) => 
    syncService.syncNoteWithLocalUpdate(db, noteData);

export const batchSyncNotes = (db: SQLiteDatabase, notes: any[], onProgress?: (current: number, total: number) => void) => 
    syncService.batchSyncNotes(db, notes, onProgress);

export const getUnsyncedNotes = (db: SQLiteDatabase, dateFilter?: string) => 
    syncService.getUnsyncedNotes(db, dateFilter);